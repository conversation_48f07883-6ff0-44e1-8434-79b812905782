# TruBackend Email Intelligence Orchestrator Dependencies

# Core Framework
fastapi>=0.115.0
uvicorn[standard]>=0.34.0
pydantic>=2.8.0
pydantic-settings>=2.9.0

# Async Support
aiohttp>=3.9.0
aiofiles>=23.2.0

# Database & Storage
supabase>=2.15.0
psycopg2-binary>=2.9.9
sqlalchemy>=2.0.23
alembic>=1.13.1
redis>=5.0.1

# AI/ML Integration
openai>=1.80.0
langchain>=0.3.0
langchain-community>=0.3.0
langchain-openai>=0.3.0
transformers>=4.36.0
torch>=2.2.0
sentence-transformers>=4.0.0

# Bielik V3 Integration
requests>=2.31.0
httpx>=0.27.0
websockets>=12.0

# Email Processing
email-validator>=2.1.0
mail-parser>=3.15.0
imapclient>=2.3.1

# Data Processing
pandas>=2.1.4
numpy>=1.26.0
python-dateutil>=2.8.2
pytz>=2023.3

# Configuration & Environment
python-dotenv>=1.0.0
pyyaml>=6.0.1
toml>=0.10.2

# Logging & Monitoring
structlog>=23.2.0
prometheus-client>=0.19.0
sentry-sdk[fastapi]>=1.39.0

# Security
cryptography>=45.0.0
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# Utilities
click>=8.1.7
rich>=13.7.0
typer>=0.9.0

# Testing (for development)
pytest>=7.4.3
pytest-asyncio>=0.21.1

# LangGraph Integration
langgraph>=0.2.0
langsmith>=0.1.0

# Executive Assistant Integration
google-auth>=2.25.0
google-auth-oauthlib>=1.2.0
google-auth-httplib2>=0.2.0
google-api-python-client>=2.110.0

# Memory Bank Integration
qdrant-client>=1.7.0
faiss-cpu>=1.7.4

# Calendar Integration
caldav>=1.3.9
icalendar>=5.0.11

# Additional Utilities
jinja2>=3.1.2
markupsafe>=2.1.3
python-multipart>=0.0.6
python-magic>=0.4.27